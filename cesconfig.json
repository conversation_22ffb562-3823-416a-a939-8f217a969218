{"cesVersion": "2.12.2", "projectName": "<PERSON><PERSON><PERSON>", "packages": [{"name": "expo-router", "type": "navigation", "options": {"type": "stack"}}, {"name": "nativewind", "type": "styling"}], "flags": {"noGit": false, "noInstall": false, "overwrite": false, "importAlias": true, "packageManager": "npm", "eas": false}, "packageManager": {"type": "npm", "version": "10.9.0"}, "os": {"type": "<PERSON>", "platform": "darwin", "arch": "arm64", "kernelVersion": "24.3.0"}}